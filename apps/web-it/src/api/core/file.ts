import type { RequestClientConfig } from '@vben/request';

import { requestClient } from '#/api/request';

export async function uploadFileApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/infra/attach/upload', data, config);
}
export async function getPreviewFileLinkApi(params: { id: number }) {
  return requestClient.get('/infra/attach/preview-link', { params });
}
export async function getDownloadFileLinkApi(params: { id: number }) {
  return requestClient.get('/infra/attach/file-link', { params });
}
export async function getOnlyOfficeFileInfoApi(fileId: number, mode: 'edit' | 'view' = 'edit', isForm?: boolean) {
  return requestClient.get(`/infra/onlyOffice/editor/${mode}`, { params: { fileId, isForm } });
}
export async function saveOnlyOfficeFileApi(fileId: number) {
  return requestClient.post('/infra/onlyOffice/save', {}, { params: { fileId } });
}
export async function getPreviewFileExternalLink(params: { id: string }, config?: RequestClientConfig) {
  return requestClient.get('/infra/attach/kk-preview-url', { params, ...config });
}
export async function getFileInfoListApi(ids: number[]) {
  return requestClient.post('/infra/attach/list', ids);
}
export async function getBusinessFileListApi(params: { businessId: number; businessType: string }) {
  return requestClient.get('/infra/attach/business/list', { params });
}
export async function copyFileApi(params: { id: number }) {
  return requestClient.post('/infra/attach/copy', {}, { params });
}
