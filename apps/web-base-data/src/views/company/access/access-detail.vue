<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AccessInfo } from '#/api';

import { ref } from 'vue';

import { prompt } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { message, Textarea } from 'ant-design-vue';

import { auditAccessApi, getAccessDetailApi, getCompanyInfoApi } from '#/api';
import CompanyBaseDetail from '#/views/company/components/company-base-detail.vue';
import CompanyLegalPersonDetail from '#/views/company/components/company-legal-person-detail.vue';

const emit = defineEmits(['ok', 'register']);
const companyInfo = ref({});
const pageType = ref('detail');
const init = async (data: AccessInfo) => {
  console.log('=== init function called ===');
  console.log('init data:', data);
  console.log('init data type:', typeof data);
  console.log('init data keys:', Object.keys(data || {}));

  pageType.value = data.pageType ?? 'detail';
  console.log('pageType set to:', pageType.value);

  if (data.companyId) {
    console.log('fetching company info for companyId:', data.companyId);
    companyInfo.value = await getCompanyInfoApi({ id: data.companyId });
    console.log('company info fetched:', companyInfo.value);
  }

  if (data.id) {
    console.log('fetching access detail for id:', data.id);
    accessForm.value = await getAccessDetailApi({ id: data.id });
    console.log('access detail fetched:', accessForm.value);
  } else {
    console.log('using provided data as accessForm');
    accessForm.value = { ...data };
  }

  await gridApi.grid.reloadData(accessForm.value.operationLogs ?? []);
  console.log('=== init function completed ===');
};
console.log('=== usePopupInner setup ===');
const [registerPopup, { closePopup }] = usePopupInner((data) => {
  console.log('usePopupInner callback triggered with data:', data);
  init(data);
});
console.log('=== usePopupInner setup completed ===');
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'operationType', title: '操作类型' },
    { field: 'operatorName', title: '操作人' },
    { field: 'operationTime', title: '操作时间', formatter: 'formatDate' },
    { field: 'remark', title: '原因' },
  ],
  ...DETAIL_GRID_OPTIONS,
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const accessForm = ref<AccessInfo>({});
const audit = async (status: number) => {
  prompt({
    content: '请输入审核意见：',
    title: '审核意见',
    component: Textarea,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入审核意见');
        return false;
      }
      await auditAccessApi({ id: accessForm.value.id as number, isPass: status, remark: scope.value });
      return true;
    },
  }).then(() => {
    message.success($t('base.resSuccess'));
    emit('ok');
    closePopup()
  });
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="准入信息" @register="registerPopup">
    <template v-if="pageType === 'audit'" #insertToolbar>
      <a-space>
        <a-button type="primary" @click="audit(1)">通过</a-button>
        <a-button type="primary" danger @click="audit(0)">驳回</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本信息 -->
      <CompanyBaseDetail :company-info="companyInfo" />
      <!-- 法人基本信息 -->
      <CompanyLegalPersonDetail :company-info="companyInfo" />
      <BasicCaption content="准入材料" />
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="备注">
          {{ accessForm.remark }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="操作记录" />
      <Grid />
    </div>
  </BasicPopup>
</template>

<style></style>
